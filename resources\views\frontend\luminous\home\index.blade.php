@extends('frontend::layouts.app', ['bodyClass' => '', 'mainClass' => ''])
@section('title')
    {{ __('Home') }}
@endsection
@push('css')
    <style>
        .swiper-wrapper {
            height: fit-content;
        }

        .common-faq-box .accordion .accordion-item .accordion-button:not(.collapsed)::before {
            background-image: url("{{ asset('global/images/accordion-arrow-plus.svg') }}") !important;
        }

        .common-faq-box .accordion .accordion-item .accordion-button::before {
            background-image: url("{{ asset('global/images/accordion-arrow-cross.svg') }}") !important;
        }
    </style>
@endpush
@section('meta_keywords')
    {{ trim(setting('meta_keywords', 'meta')) }}
@endsection
@section('meta_description')
    {{ trim(setting('meta_description', 'meta')) }}
@endsection

@section('content')
@foreach($homeContent as $content)
    @php
        $data = json_decode($content->data, true);
    @endphp
    @include('frontend::home.include.__' . $content->code, ['data' => $data])

@endforeach
@endsection