{"__meta": {"id": "01JY15SZZ8KW3JP3PSTHPHD9H1", "datetime": "2025-06-18 15:27:02", "utime": **********.376972, "method": "GET", "uri": "/admin/clear-cache", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.978966, "end": **********.376988, "duration": 0.***************, "duration_str": "398ms", "measures": [{"label": "Booting", "start": **********.978966, "relative_start": 0, "end": **********.167291, "relative_end": **********.167291, "duration": 0.****************, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.174936, "relative_start": 0.****************, "end": **********.17842, "relative_end": **********.17842, "duration": 0.003484010696411133, "duration_str": "3.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.374874, "relative_start": 0.****************, "end": **********.375176, "relative_end": **********.375176, "duration": 0.0003018379211425781, "duration_str": "302μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/clear-cache", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check", "controller": "App\\Http\\Controllers\\Backend\\AppController@clearCache<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Fgamecon%2Fapp%2FHttp%2FControllers%2FBackend%2FAppController.php:149\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.clear-cache", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Fgamecon%2Fapp%2FHttp%2FControllers%2FBackend%2FAppController.php:149\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/AppController.php:149-156</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00078, "accumulated_duration_str": "780μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\gamecon\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\gamecon\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\gamecon\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\gamecon\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\gamecon\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.185171, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\gamecon\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Fgamecon%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gamecon", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Fgamecon%2Fapp%2FModels%2FAdmin.php:1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1DDrWxVmrM9vi6ZM7qtWNZilItOF8V6oEQhpOWCG", "url": "array:1 [\n  \"intended\" => \"https://gamecon.test/user/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:1 [\n    0 => \"notify\"\n  ]\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "11", "_previous": "array:1 [\n  \"url\" => \"https://gamecon.test/admin/page/section/faq\"\n]", "site-color-mode": "null", "google2fa": "array:3 [\n  \"auth_passed\" => true\n  \"otp_timestamp\" => true\n  \"auth_time\" => \"2025-06-18T13:04:19+06:00\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "notify": "array:3 [\n  \"type\" => \"success\"\n  \"message\" => \"<PERSON><PERSON> cleared successfully!\"\n  \"title\" => \"Success\"\n]"}, "request": {"data": {"status": "302 Found", "full_url": "https://gamecon.test/admin/clear-cache", "action_name": "admin.clear-cache", "controller_action": "App\\Http\\Controllers\\Backend\\AppController@clearCache", "uri": "GET admin/clear-cache", "controller": "App\\Http\\Controllers\\Backend\\AppController@clearCache<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Fgamecon%2Fapp%2FHttp%2FControllers%2FBackend%2FAppController.php:149\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Fgamecon%2Fapp%2FHttp%2FControllers%2FBackend%2FAppController.php:149\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/AppController.php:149-156</a>", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check", "duration": "399ms", "peak_memory": "38MB", "response": "Redirect to https://gamecon.test/admin/page/section/faq", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1750537515 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1750537515\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-319931968 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-319931968\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-939285885 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1282 characters\">gdpr_cookies=eyJpdiI6IktGTThXa2hyeERrNWczRDRXN1Yzanc9PSIsInZhbHVlIjoiZ04vYVBobWtPUFNVUzNydlM0V3dJaWc5aStDcC9CeDI3VDIrU2dMbGJKdUlNSk5pNkxBck81NTJxV0t1YWFuMiIsIm1hYyI6IjM4OTZmMGI1OGNjNzE1NGZhYjc2MDE4MzdmNjYyMDFkNGNkZTc4NjJlNDIwOWY1OTM1OTNhYTM0Y2JlYzBhMDQiLCJ0YWciOiIifQ%3D%3D; reject_signup_first_order_bonus=eyJpdiI6IjIrRUVEazUyaEVLWUtlRW1Ja29jdlE9PSIsInZhbHVlIjoiWDd4QmNvRVA0MTVBWTNPUEY3Q2dSQ0N0MjBYQklrT2tFV0VtL2YyMGY5VldIQnVObkNUdW42Y1NkTGIxRytUMiIsIm1hYyI6ImMwODExMmE0ZTcwNGQxYjRlZTg0MjJkMWJiMTM5NmE0NzI2MTJjNzNmYTRmNWU5ODIyOGM1MWRiYjI1YThmOTAiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6InZ6SS9jOTl0eXJmSGlhQW5uNzd3L2c9PSIsInZhbHVlIjoiaFZhdFc4Kzl0cFFZdkhFR1ZKM2Y4OHhYbDVRdGNLZmR3TEhiUC9qQ2RkZ3ZGTDlHc3gxT2hCT3RBTVNham9sYXh1OE5aNitoU1grbTR2aU1nSFBuT3J0VkJ1NGJsZE5VamRtZENFdUVhblRvSHpRV0FLNytQczhMSnpzWTJPRVMiLCJtYWMiOiJmYzlmNzFhZDI3MzM2N2RkYWZiOTBmZWM4MTU2ZjFiYzJlNGVjZjk1MTI2OTRlMTM1MjdiYTY5Zjc1MWVkODIxIiwidGFnIjoiIn0%3D; gamecon_session=eyJpdiI6IkcrMk02NDVuOWtnQmdmUkVabTczN2c9PSIsInZhbHVlIjoiZmpGcm5kZWFnZnZtdVBsMVI3K0lIZFF0QW9xL3FyNVkvY1JiazNEdGxVVmVzbUJKUFJ0QzBnZjdvaHJtd05JMTJ1a0JIaEZtVlk5V0c1MG45VXp3aFVnV3hiSzFXdE9mbnZNdEs0RTVrMForWlJMUTdlOGo1d2NwK3dqRElXeVciLCJtYWMiOiJiMTRmNDM2MDM3NjQzZTc3ZWE5ZDY1YzRlYmU2YWNhYmJmNTRhZTE2MDJkNDFjOTdhMzA1ZDc0NjU1OGI2NjMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">https://gamecon.test/admin/page/section/faq</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">gamecon.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-939285885\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1486189296 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>gdpr_cookies</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>reject_signup_first_order_bonus</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1DDrWxVmrM9vi6ZM7qtWNZilItOF8V6oEQhpOWCG</span>\"\n  \"<span class=sf-dump-key>gamecon_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IGuKPKHCa1wogOcGUhdNuLdlrcqaN6P20Fd3WQ1b</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486189296\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1647985306 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 09:27:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">https://gamecon.test/admin/page/section/faq</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647985306\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2121325575 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1DDrWxVmrM9vi6ZM7qtWNZilItOF8V6oEQhpOWCG</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"35 characters\">https://gamecon.test/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">notify</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">https://gamecon.test/admin/page/section/faq</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>site-color-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>google2fa</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>auth_passed</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>otp_timestamp</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>auth_time</span>\" => \"<span class=sf-dump-str title=\"25 characters\">2025-06-18T13:04:19+06:00</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>notify</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Cache cleared successfully!</span>\"\n    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121325575\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://gamecon.test/admin/clear-cache", "action_name": "admin.clear-cache", "controller_action": "App\\Http\\Controllers\\Backend\\AppController@clearCache"}, "badge": "302 Found"}}